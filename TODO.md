# AgentPressComposioToolSet Migration Plan - TODO

## 📋 **Executive Summary**

Complete migration from manual XML mapping (`ComposioXMLToolFactory` + `UniversalComposioXMLTool`) to automatic schema conversion system (`AgentPressComposioToolSet`). Execute in 4 phases with zero downtime and full backward compatibility.

## 🎯 **Migration Objectives**

### **Primary Goals:**
1. **Replace Manual Schema Generation** with automatic conversion from Composio's JSON schemas
2. **Enable Dynamic Tool Discovery** based on user's active connections
3. **Reduce Code Complexity** by eliminating 1000+ lines of manual mapping code
4. **Improve Scalability** to support 100+ integrations without manual configuration
5. **Maintain Zero Breaking Changes** during transition

### **Success Criteria:**
- ✅ AI agent can dynamically discover user tools based on `user_mcp_connections` table
- ✅ Natural language tool calling works seamlessly with new system
- ✅ All existing parameter mappings preserved and enhanced
- ✅ Performance equal or better than current system
- ✅ Complete test coverage with real user scenarios

## 🏗️ **Phase-by-Phase Migration Plan**

### ✅ **Phase 1: Test Setup & Base Implementation** (COMPLETED)
- [x] Updated `backend/test_e2e_composio_agent.py` with new test functions
- [x] Created `backend/agent/tools/agentpress_composio_toolset.py` (343 lines)
- [x] Implemented `AgentPressXMLTool` and `AgentPressComposioToolSet` classes
- [x] Basic functionality validated with import and instantiation tests

### 🔄 **Phase 2: Core Functionality Implementation** (CURRENT)

#### **2.1 Enhanced AgentPressComposioToolSet Implementation**

**File:** `backend/agent/tools/agentpress_composio_toolset.py`

**Tasks:**
- [ ] Implement enhanced `_wrap_tool()` method with comprehensive parameter mapping
- [ ] Add intelligent parameter categorization (XML attributes vs content)
- [ ] Implement `_generate_enhanced_xml_schema()` method
- [ ] Add `_should_be_xml_content()` helper for parameter categorization
- [ ] Add `_get_user_friendly_param_name()` for parameter mapping
- [ ] Add `_build_xml_schema_string()` for XML schema generation

**Code to Add:**
```python
def _wrap_tool(self, schema: Dict[str, Any], entity_id: Optional[str] = None) -> AgentPressXMLTool:
    """Enhanced tool wrapping with comprehensive parameter mapping"""
    action_name = schema["name"]
    description = schema["description"] 
    schema_params = schema.get("parameters", {})
    app_name = schema.get("appName", "unknown")
    
    service_name = self._extract_service_name_enhanced(action_name, app_name)
    xml_schema = self._generate_enhanced_xml_schema(action_name, service_name, schema_params, description)
    
    return AgentPressXMLTool(
        action_name=action_name,
        service_name=service_name,
        description=description,
        schema_params=schema_params,
        xml_schema=xml_schema,
        toolset=self,
        entity_id=entity_id or self.entity_id,
    )

def _generate_enhanced_xml_schema(self, action_name: str, service_name: str, schema_params: Dict[str, Any], description: str) -> str:
    """Generate enhanced XML schema using existing parameter mappings"""
    # Implementation needed - categorize parameters, build XML schema string
    pass

def _should_be_xml_content(self, param_info: Dict[str, Any]) -> bool:
    """Determine if parameter should be XML content vs attribute"""
    # Implementation needed - check param type, length, description
    pass
```

#### **2.2 Dynamic User Connection Integration**

**File:** `backend/agent/tools/agentpress_composio_toolset.py`

**Tasks:**
- [ ] Implement `get_user_tools_dynamic()` method
- [ ] Add integration with `ComposioAuthService` for connection discovery
- [ ] Add service name to App enum mapping
- [ ] Add connection filtering logic

**Code to Add:**
```python
async def get_user_tools_dynamic(self, user_id: str) -> List[AgentPressXMLTool]:
    """Dynamically discover and create XML tools based on user's active connections"""
    from services.composio_auth_service import ComposioAuthService
    
    auth_service = ComposioAuthService.from_env()
    active_connections = await auth_service.get_user_connections(user_id)
    
    active_services = [conn.service_name for conn in active_connections if conn.status == "active" and conn.is_active]
    
    if not active_services:
        return []
    
    apps_to_load = [self._get_app_enum(service_name) for service_name in active_services if self._get_app_enum(service_name)]
    
    xml_tools = self.get_tools(apps=apps_to_load, entity_id=user_id, check_connected_accounts=True)
    
    logger.info(f"🎉 Dynamically loaded {len(xml_tools)} tools for user {user_id}")
    return xml_tools
```

#### **2.3 Enhanced AgentPressXMLTool Class**

**File:** `backend/agent/tools/agentpress_composio_toolset.py`

**Tasks:**
- [ ] Add `get_xml_mappings()` method to AgentPressXMLTool
- [ ] Add `get_xml_example()` method to AgentPressXMLTool
- [ ] Enhance `execute()` method with better error handling
- [ ] Add XML schema caching for performance

### 🔄 **Phase 3: Agent Integration**

#### **3.1 ThreadManager Integration**

**File:** `backend/agentpress/thread_manager.py`

**Tasks:**
- [ ] Add `load_composio_tools_dynamic()` method to ThreadManager class
- [ ] Integrate with existing `tool_registry.xml_tools` system
- [ ] Add fallback mechanism to existing system
- [ ] Add comprehensive logging for debugging

**Code to Add:**
```python
async def load_composio_tools_dynamic(self, user_id: str) -> None:
    """Load Composio tools dynamically using AgentPressComposioToolSet"""
    try:
        from agent.tools.agentpress_composio_toolset import AgentPressComposioToolSet
        
        composio_toolset = AgentPressComposioToolSet(
            api_key=os.getenv("COMPOSIO_API_KEY"),
            entity_id=user_id
        )
        
        xml_tools = await composio_toolset.get_user_tools_dynamic(user_id)
        
        for xml_tool in xml_tools:
            xml_schema = XMLTagSchema(
                tag_name=xml_tool.xml_tag,
                mappings=xml_tool.get_xml_mappings(),
                example=xml_tool.get_xml_example()
            )
            
            self.tool_registry.xml_tools[xml_tool.xml_tag] = {
                "instance": xml_tool,
                "method": f"execute_{xml_tool.service_name}_{xml_tool.action_name.lower()}",
                "schema": xml_schema,
                "service_name": xml_tool.service_name,
                "action_name": xml_tool.action_name
            }
        
        logger.info(f"🎉 Dynamically registered {len(xml_tools)} Composio XML tools for user {user_id}")
        
    except Exception as e:
        logger.error(f"❌ Failed to load dynamic Composio tools: {e}")
        await self._fallback_to_existing_composio_tools(user_id)
```

#### **3.2 Agent Run Integration**

**File:** `backend/agent/run.py`

**Tasks:**
- [ ] Modify `run_agent()` function to use new dynamic tool loading
- [ ] Add fallback to existing system if new system fails
- [ ] Add comprehensive logging for tool loading
- [ ] Add performance monitoring

**Code to Modify:**
```python
# In run_agent function, replace existing Composio tool loading with:
if user_id:
    try:
        logger.info(f"🔗 Loading dynamic Composio XML tools for user {user_id}")
        await thread_manager.load_composio_tools_dynamic(user_id)
        
        composio_tools = [tool_name for tool_name in thread_manager.tool_registry.xml_tools.keys() if tool_name.endswith('-action')]
        
        if composio_tools:
            logger.info(f"✅ Loaded {len(composio_tools)} dynamic Composio tools")
        else:
            logger.warning(f"⚠️ No Composio tools loaded for user {user_id}")
            
    except Exception as e:
        logger.error(f"❌ Error loading dynamic Composio tools: {e}")
        # Fallback to existing system
```

### 🔄 **Phase 4: Testing & Validation**

#### **4.1 Create Comprehensive Test Suite**

**File:** `backend/test_agentpress_composio_migration.py` (NEW)

**Tasks:**
- [ ] Create comprehensive test file with real user scenarios
- [ ] Implement `test_dynamic_tool_discovery()` function
- [ ] Implement `test_natural_language_tool_calling()` function  
- [ ] Implement `test_parameter_mapping_accuracy()` function
- [ ] Implement `test_performance_comparison()` function
- [ ] Add test data and user configurations

**Test Users:**
```python
TEST_USERS = {
    "gmail_user": "f7f2d110-7af2-41b1-a23c-3c6a3c866689",  # User with Gmail
    "notion_user": "13b2f118-a40f-4dc2-b87e-ce874b39a30f",  # User with Gmail + Notion
}
```

**Natural Language Test Cases:**
```python
NATURAL_LANGUAGE_TESTS = [
    {
        "user": "gmail_user",
        "prompt": "Send an <NAME_EMAIL> with subject 'Test Email' and body 'This is a test email.'",
        "expected_tools": ["gmail-action"],
        "expected_actions": ["GMAIL_SEND_EMAIL"]
    },
    {
        "user": "notion_user", 
        "prompt": "Create a new page in Notion titled 'Meeting Notes' with content about today's standup.",
        "expected_tools": ["notion-action"],
        "expected_actions": ["NOTION_CREATE_NOTION_PAGE"]
    },
    {
        "user": "notion_user",
        "prompt": "Search Gmail for emails from last week and create a Notion page summarizing them.",
        "expected_tools": ["gmail-action", "notion-action"],
        "expected_actions": ["GMAIL_FETCH_EMAILS", "NOTION_CREATE_NOTION_PAGE"]
    }
]
```

#### **4.2 Enhanced Existing Tests**

**File:** `backend/test_e2e_composio_agent.py`

**Tasks:**
- [ ] Update existing tests to use new system
- [ ] Add comparison tests between old and new systems
- [ ] Add performance benchmarking
- [ ] Add error handling validation

## 🚀 **Execution Instructions**

### **Step 1: Execute Phase 2**
```bash
cd backend
# Implement enhanced AgentPressComposioToolSet
# Test dynamic tool discovery
python -c "
from agent.tools.agentpress_composio_toolset import AgentPressComposioToolSet
import asyncio

async def test():
    toolset = AgentPressComposioToolSet(api_key='d4duyheb02jeq5gtny1qh', entity_id='test_user')
    tools = await toolset.get_user_tools_dynamic('13b2f118-a40f-4dc2-b87e-ce874b39a30f')
    print(f'Loaded {len(tools)} tools')

asyncio.run(test())
"
```

### **Step 2: Execute Phase 3**
```bash
# Test ThreadManager integration
python -c "
from agentpress.thread_manager import ThreadManager
import asyncio

async def test():
    tm = ThreadManager()
    await tm.load_composio_tools_dynamic('13b2f118-a40f-4dc2-b87e-ce874b39a30f')
    print(f'Registered tools: {list(tm.tool_registry.xml_tools.keys())}')

asyncio.run(test())
"
```

### **Step 3: Execute Phase 4**
```bash
# Run comprehensive tests
python test_agentpress_composio_migration.py
python test_e2e_composio_agent.py
```

### **Step 4: Validation**
```bash
# Test natural language prompting
python -c "
from agent.run import run_agent
import asyncio

async def test_nl():
    # Test with real user and natural language prompt
    async for chunk in run_agent(
        thread_id='test_thread',
        project_id='test_project', 
        user_id='13b2f118-a40f-4dc2-b87e-ce874b39a30f',
        stream=True
    ):
        print(chunk)
        break

asyncio.run(test_nl())
"
```

## 📊 **Success Metrics**

- [ ] **Dynamic Tool Discovery**: Tools loaded based on user connections (not hardcoded)
- [ ] **Natural Language Success**: Agent can execute tools from natural language prompts
- [ ] **Parameter Mapping**: User-friendly parameters correctly mapped to Composio format
- [ ] **Performance**: Equal or better performance than existing system
- [ ] **Error Handling**: Graceful fallback when new system fails
- [ ] **Code Reduction**: Significant reduction in manual mapping code

## 🔧 **Rollback Plan**

If issues arise:
1. **Phase 2 Issues**: Revert `agentpress_composio_toolset.py` changes
2. **Phase 3 Issues**: Revert `thread_manager.py` and `run.py` changes  
3. **Phase 4 Issues**: Use existing test suite
4. **Complete Rollback**: Remove new system, keep existing `ComposioXMLToolFactory`

## 📝 **Notes**

- **Test Users**: Use real test users from `user_mcp_connections_rows.csv`
- **API Key**: Use `COMPOSIO_API_KEY=d4duyheb02jeq5gtny1qh` for testing
- **Backward Compatibility**: Maintain existing XML tool calling interface
- **Performance**: Monitor tool loading and execution times
- **Logging**: Add comprehensive logging for debugging and monitoring
