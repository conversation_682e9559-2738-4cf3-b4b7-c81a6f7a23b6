2025-05-30 15:58:05 2025-05-30 10:28:05,246 - httpcore.http2 - DEBUG - send_request_headers.complete
2025-05-30 15:58:05 2025-05-30 10:28:05,246 - httpcore.http2 - DEBUG - send_request_body.started request=<Request [b'GET']> stream_id=11
2025-05-30 15:58:05 2025-05-30 10:28:05,246 - httpcore.http2 - DEBUG - send_request_body.complete
2025-05-30 15:58:05 2025-05-30 10:28:05,246 - httpcore.http2 - DEBUG - receive_response_headers.started request=<Request [b'GET']> stream_id=11
2025-05-30 15:58:05 2025-05-30 10:28:05,258 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping GMAIL_DELETE_DRAFT as XML tool for service gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,258 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: gmail_gmail_delete_draft
2025-05-30 15:58:05 2025-05-30 10:28:05,258 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping GMAIL_DELETE_MESSAGE as XML tool for service gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,258 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: gmail_gmail_delete_message
2025-05-30 15:58:05 2025-05-30 10:28:05,258 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping GMAIL_FETCH_EMAILS as XML tool for service gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: gmail_gmail_fetch_emails
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping GMAIL_GET_CONTACTS as XML tool for service gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: gmail_gmail_get_contacts
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping GMAIL_LIST_DRAFTS as XML tool for service gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: gmail_gmail_list_drafts
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping GMAIL_MOVE_TO_TRASH as XML tool for service gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: gmail_gmail_move_to_trash
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping GMAIL_REPLY_TO_THREAD as XML tool for service gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: gmail_gmail_reply_to_thread
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping GMAIL_SEARCH_PEOPLE as XML tool for service gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: gmail_gmail_search_people
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping GMAIL_SEND_EMAIL as XML tool for service gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: gmail_gmail_send_email
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping GOOGLEDRIVE_UPLOAD_FILE as XML tool for service google_drive
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: google_drive_googledrive_upload_file
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_ADD_PAGE_CONTENT as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_add_page_content
2025-05-30 15:58:05 2025-05-30 10:28:05,259 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_CREATE_COMMENT as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_create_comment
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_CREATE_DATABASE as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_create_database
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_CREATE_NOTION_PAGE as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_create_notion_page
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_DELETE_BLOCK as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_delete_block
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_FETCH_COMMENTS as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_fetch_comments
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_FETCH_DATABASE as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_fetch_database
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_FETCH_ROW as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_fetch_row
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_GET_ABOUT_ME as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_get_about_me
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_GET_ABOUT_USER as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_get_about_user
2025-05-30 15:58:05 2025-05-30 10:28:05,260 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_INSERT_ROW_DATABASE as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_insert_row_database
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_LIST_USERS as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_list_users
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_QUERY_DATABASE as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_query_database
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_UPDATE_ROW_DATABASE as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_update_row_database
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping NOTION_UPDATE_SCHEMA_DATABASE as XML tool for service notion
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: notion_notion_update_schema_database
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping SLACK_ADD_REACTION_TO_AN_ITEM as XML tool for service slack
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: slack_slack_add_reaction_to_an_item
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping SLACK_CREATE_A_REMINDER as XML tool for service slack
2025-05-30 15:58:05 2025-05-30 10:28:05,261 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: slack_slack_create_a_reminder
2025-05-30 15:58:05 2025-05-30 10:28:05,262 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping SLACK_FETCH_CONVERSATION_HISTORY as XML tool for service slack
2025-05-30 15:58:05 2025-05-30 10:28:05,262 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: slack_slack_fetch_conversation_history
2025-05-30 15:58:05 2025-05-30 10:28:05,263 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping SLACK_LIST_ALL_SLACK_TEAM_CHANNELS_WITH_VARIOUS_FILTERS as XML tool for service slack
2025-05-30 15:58:05 2025-05-30 10:28:05,263 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: slack_slack_list_all_slack_team_channels_with_various_filters
2025-05-30 15:58:05 2025-05-30 10:28:05,263 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping SLACK_LIST_ALL_SLACK_TEAM_USERS_WITH_PAGINATION as XML tool for service slack
2025-05-30 15:58:05 2025-05-30 10:28:05,263 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: slack_slack_list_all_slack_team_users_with_pagination
2025-05-30 15:58:05 2025-05-30 10:28:05,263 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping SLACK_REMOVE_REACTION_FROM_ITEM as XML tool for service slack
2025-05-30 15:58:05 2025-05-30 10:28:05,263 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: slack_slack_remove_reaction_from_item
2025-05-30 15:58:05 2025-05-30 10:28:05,263 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping SLACK_SCHEDULES_A_MESSAGE_TO_A_CHANNEL_AT_A_SPECIFIED_TIME as XML tool for service slack
2025-05-30 15:58:05 2025-05-30 10:28:05,264 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: slack_slack_schedules_a_message_to_a_channel_at_a_specified_time
2025-05-30 15:58:05 2025-05-30 10:28:05,264 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping SLACK_SEARCH_FOR_MESSAGES_WITH_QUERY as XML tool for service slack
2025-05-30 15:58:05 2025-05-30 10:28:05,264 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: slack_slack_search_for_messages_with_query
2025-05-30 15:58:05 2025-05-30 10:28:05,264 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping SLACK_SENDS_A_MESSAGE_TO_A_SLACK_CHANNEL as XML tool for service slack
2025-05-30 15:58:05 2025-05-30 10:28:05,264 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: slack_slack_sends_a_message_to_a_slack_channel
2025-05-30 15:58:05 2025-05-30 10:28:05,264 - agent.tools.agentpress_composio_toolset - DEBUG - 🔧 Wrapping SLACK_UPDATES_A_SLACK_MESSAGE as XML tool for service slack
2025-05-30 15:58:05 2025-05-30 10:28:05,264 - agent.tools.agentpress_composio_toolset - DEBUG - ✅ Created XML tool: slack_slack_updates_a_slack_message
2025-05-30 15:58:05 2025-05-30 10:28:05,264 - agent.tools.agentpress_composio_toolset - INFO - 🎉 Created 35 XML tools from 35 schemas
2025-05-30 15:58:05 2025-05-30 10:28:05,265 - agent.tools.agentpress_composio_toolset - INFO - 🎉 Dynamically loaded 35 tools for user f7f2d110-7af2-41b1-a23c-3c6a3c866689
2025-05-30 15:58:05 2025-05-30 10:28:05,265 - agentpress - DEBUG - ✅ Registered XML tool: gmail-action for gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,265 - agentpress - DEBUG - ✅ Registered XML tool: gmail-action for gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,265 - agentpress - DEBUG - ✅ Registered XML tool: gmail-action for gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,265 - agentpress - DEBUG - ✅ Registered XML tool: gmail-action for gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,266 - agentpress - DEBUG - ✅ Registered XML tool: gmail-action for gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,266 - agentpress - DEBUG - ✅ Registered XML tool: gmail-action for gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,266 - agentpress - DEBUG - ✅ Registered XML tool: gmail-action for gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,266 - agentpress - DEBUG - ✅ Registered XML tool: gmail-action for gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,266 - agentpress - DEBUG - ✅ Registered XML tool: gmail-action for gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,266 - agentpress - DEBUG - ✅ Registered XML tool: google_drive-action for google_drive
2025-05-30 15:58:05 2025-05-30 10:28:05,266 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,266 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,266 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,267 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,267 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,267 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,267 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,267 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,267 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,267 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,267 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,267 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,267 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,268 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,268 - agentpress - DEBUG - ✅ Registered XML tool: notion-action for notion
2025-05-30 15:58:05 2025-05-30 10:28:05,268 - agentpress - DEBUG - ✅ Registered XML tool: slack-action for slack
2025-05-30 15:58:05 2025-05-30 10:28:05,268 - agentpress - DEBUG - ✅ Registered XML tool: slack-action for slack
2025-05-30 15:58:05 2025-05-30 10:28:05,268 - agentpress - DEBUG - ✅ Registered XML tool: slack-action for slack
2025-05-30 15:58:05 2025-05-30 10:28:05,268 - agentpress - DEBUG - ✅ Registered XML tool: slack-action for slack
2025-05-30 15:58:05 2025-05-30 10:28:05,268 - agentpress - DEBUG - ✅ Registered XML tool: slack-action for slack
2025-05-30 15:58:05 2025-05-30 10:28:05,268 - agentpress - DEBUG - ✅ Registered XML tool: slack-action for slack
2025-05-30 15:58:05 2025-05-30 10:28:05,269 - agentpress - DEBUG - ✅ Registered XML tool: slack-action for slack
2025-05-30 15:58:05 2025-05-30 10:28:05,269 - agentpress - DEBUG - ✅ Registered XML tool: slack-action for slack
2025-05-30 15:58:05 2025-05-30 10:28:05,269 - agentpress - DEBUG - ✅ Registered XML tool: slack-action for slack
2025-05-30 15:58:05 2025-05-30 10:28:05,269 - agentpress - DEBUG - ✅ Registered XML tool: slack-action for slack
2025-05-30 15:58:05 2025-05-30 10:28:05,269 - agentpress - INFO - 🎉 Dynamically registered 35 Composio XML tools for user f7f2d110-7af2-41b1-a23c-3c6a3c866689
2025-05-30 15:58:05 2025-05-30 10:28:05,269 - agentpress - INFO - ✅ Loaded 4 dynamic Composio tools
2025-05-30 15:58:05 2025-05-30 10:28:05,269 - agentpress - DEBUG -    📋 gmail-action: gmail
2025-05-30 15:58:05 2025-05-30 10:28:05,269 - agentpress - DEBUG -    📋 google_drive-action: google_drive
2025-05-30 15:58:05 2025-05-30 10:28:05,269 - agentpress - DEBUG -    📋 notion-action: notion
2025-05-30 15:58:05 2025-05-30 10:28:05,269 - agentpress - DEBUG -    📋 slack-action: slack
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_SEND_EMAIL
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_CREATE_EMAIL_DRAFT
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_FETCH_EMAILS
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_REPLY_TO_THREAD
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_DELETE_MESSAGE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_MOVE_TO_TRASH
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_LIST_DRAFTS
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_DELETE_DRAFT
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_ADD_LABEL_TO_EMAIL
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_CREATE_LABEL
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_LIST_LABELS
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_REMOVE_LABEL
2025-05-30 15:58:05 2025-05-30 10:28:05,269 - INFO - 🎉 Dynamically registered 35 Composio XML tools for user f7f2d110-7af2-41b1-a23c-3c6a3c866689
2025-05-30 15:58:05 2025-05-30 10:28:05,269 - INFO - ✅ Loaded 4 dynamic Composio tools
2025-05-30 15:58:05 2025-05-30 10:28:05,312 - INFO - Running in local development mode - billing checks are disabled
2025-05-30 15:58:05 172.18.0.1:58902 - "OPTIONS /api/project/dd30cfa9-bd6c-43e5-b7f7-32357e97f19d/sandbox/ensure-active HTTP/1.1" 200
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_GET_PROFILE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_GET_CONTACTS
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for gmail.GMAIL_SEARCH_PEOPLE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_CREATE_NOTION_PAGE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_SEARCH_NOTION_PAGE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_FETCH_ROW
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_ADD_PAGE_CONTENT
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_APPEND_BLOCK_CHILDREN
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_CREATE_DATABASE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_QUERY_DATABASE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_INSERT_ROW_DATABASE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_UPDATE_ROW_DATABASE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_FETCH_DATABASE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_ARCHIVE_NOTION_PAGE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_DELETE_BLOCK
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_FETCH_NOTION_BLOCK
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_FETCH_NOTION_CHILD_BLOCK
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_CREATE_COMMENT
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_FETCH_COMMENTS
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_DUPLICATE_PAGE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_NOTION_UPDATE_BLOCK
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_UPDATE_SCHEMA_DATABASE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_GET_ABOUT_ME
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_GET_ABOUT_USER
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_LIST_USERS
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for notion.NOTION_GET_PAGE_PROPERTY_ACTION
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for slack.SLACK_CHAT_POST_MESSAGE
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for slack.SLACK_ADD_REACTION_TO_AN_ITEM
2025-05-30 15:58:05 2025-05-30 10:28:05,310 - services.composio_processors - DEBUG - Created processors for slack.SLACK_CREATE_A_REMINDER
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for slack.SLACK_DELETES_A_MESSAGE_FROM_A_CHAT
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for slack.SLACK_CREATE_CHANNEL_BASED_CONVERSATION
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for slack.SLACK_ARCHIVE_A_SLACK_CONVERSATION
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for slack.SLACK_CONVERSATIONS_LIST
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for slack.SLACK_CONVERSATIONS_INFO
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for slack.SLACK_CONVERSATIONS_HISTORY
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for googledrive.GOOGLEDRIVE_CREATE_FILE_FROM_TEXT
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for googledrive.GOOGLEDRIVE_CREATE_FOLDER
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for googledrive.GOOGLEDRIVE_FIND_FILE
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for googledrive.GOOGLEDRIVE_FIND_FOLDER
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for googledrive.GOOGLEDRIVE_DOWNLOAD_FILE
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for googledrive.GOOGLEDRIVE_EDIT_FILE
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for googledrive.GOOGLEDRIVE_COPY_FILE
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for googledrive.GOOGLEDRIVE_DELETE_FOLDER_OR_FILE
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for googledrive.GOOGLEDRIVE_UPLOAD_FILE
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_processors - DEBUG - Created processors for googledrive.GOOGLEDRIVE_ADD_FILE_SHARING_PREFERENCE
2025-05-30 15:58:05 2025-05-30 10:28:05,311 - services.composio_openai_service - INFO - Initialized Composio XML service with processors for 58 actions
2025-05-30 15:58:05 2025-05-30 10:28:05,312 - agentpress - INFO - Running in local development mode - billing checks are disabled
2025-05-30 15:58:05 2025-05-30 10:28:05,313 - httpcore.http2 - DEBUG - send_request_headers.started request=<Request [b'GET']> stream_id=21
2025-05-30 15:58:05 2025-05-30 10:28:05,313 - hpack.hpack - DEBUG - Adding (b':method', b'GET') to the header table, sensitive:False, huffman:True
2025-05-30 15:58:05 2025-05-30 10:28:05,313 - hpack.hpack - DEBUG - Encoding 2 with 7 bits
2025-05-30 15:58:05 2025-05-30 10:28:05,313 - hpack.hpack - DEBUG - Adding (b':authority', b'eygeicggvuphidsdrbac.supabase.co') to the header table, sensitive:False, huffman:True
2025-05-30 15:58:05 2025-05-30 10:28:05,313 - hpack.hpack - DEBUG - Encoding 83 with 7 bits
2025-05-30 15:58:05 2025-05-30 10:28:05,313 - hpack.hpack - DEBUG - Adding (b':scheme', b'https') to the header table, sensitive:False, huffman:True
2025-05-30 15:58:05 2025-05-30 10:28:05,313 - hpack.hpack - DEBUG - Encoding 7 with 7 bits
2025-05-30 15:58:05 2025-05-30 10:28:05,313 - hpack.hpack - DEBUG - Adding (b':path', b'/rest/v1/messages?select=%2A&thread_id=eq.195c4895-d81c-451a-aac9-ac55f48a7d30&type=in.%28assistant%2Ctool%2Cuser%29&order=created_at.desc&limit=1') to the header table, sensitive:False, huffman:True



errors:


(b'alt-svc', b'h3=":443"; ma=86400'), consumed 1
2025-05-30 15:58:11 2025-05-30 10:28:11,267 - httpcore.http2 - DEBUG - receive_response_headers.complete return_value=(201, [(b'date', b'Fri, 30 May 2025 10:28:11 GMT'), (b'content-type', b'application/json; charset=utf-8'), (b'content-range', b'*/*'), (b'cf-ray', b'947d8b10acd55518-DEL'), (b'cf-cache-status', b'DYNAMIC'), (b'content-encoding', b'gzip'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'vary', b'Accept-Encoding'), (b'content-profile', b'public'), (b'preference-applied', b'return=representation'), (b'sb-gateway-version', b'1'), (b'sb-project-ref', b'eygeicggvuphidsdrbac'), (b'x-content-type-options', b'nosniff'), (b'x-envoy-attempt-count', b'1'), (b'x-envoy-upstream-service-time', b'5'), (b'server', b'cloudflare'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-05-30 15:58:11 2025-05-30 10:28:11,273 - httpx - INFO - HTTP Request: POST https://eygeicggvuphidsdrbac.supabase.co/rest/v1/messages "HTTP/2 201 Created"
2025-05-30 15:58:11 2025-05-30 10:28:11,274 - httpcore.http2 - DEBUG - receive_response_body.started request=<Request [b'POST']> stream_id=37
2025-05-30 15:58:11 2025-05-30 10:28:11,287 - httpcore.http2 - DEBUG - receive_response_body.complete
2025-05-30 15:58:11 2025-05-30 10:28:11,288 - httpcore.http2 - DEBUG - response_closed.started stream_id=37
2025-05-30 15:58:11 2025-05-30 10:28:11,288 - httpcore.http2 - DEBUG - response_closed.complete
2025-05-30 15:58:11 2025-05-30 10:28:11,288 - agentpress - INFO - Successfully added message to thread 195c4895-d81c-451a-aac9-ac55f48a7d30
2025-05-30 15:58:11 2025-05-30 10:28:11,291 - agentpress - DEBUG - Reached XML tool call limit (1)
2025-05-30 15:58:11 2025-05-30 10:28:11,291 - agentpress - INFO - Stopping stream processing after loop due to XML tool call limit
2025-05-30 15:58:11 2025-05-30 10:28:11,291 - agentpress - INFO - Waiting for 1 pending streamed tool executions
2025-05-30 15:58:11 2025-05-30 10:28:11,292 - agentpress - INFO - Executing tool: execute_slack_slack_updates_a_slack_message with arguments: {'action': 'SLACK_GET_MENTIONS', 'as_user': '', 'attachments': '', 'blocks': '', 'channel': '', 'link_names': '', 'parse': '', 'text': '', 'ts': ''}
2025-05-30 15:58:11 2025-05-30 10:28:11,295 - agentpress - DEBUG - Received 1 new responses for f7bb413e-42c1-4b95-891e-95b4f40c724c (index 14 onwards)
2025-05-30 15:58:11 2025-05-30 10:28:11,292 - agentpress - ERROR - Error executing tool execute_slack_slack_updates_a_slack_message: 'AgentPressXMLTool' object has no attribute 'execute_gmail_gmail_send_email'
2025-05-30 15:58:11 2025-05-30 10:28:11,288 - INFO - Successfully added message to thread 195c4895-d81c-451a-aac9-ac55f48a7d30
2025-05-30 15:58:11 2025-05-30 10:28:11,291 - INFO - Stopping stream processing after loop due to XML tool call limit
2025-05-30 15:58:11 2025-05-30 10:28:11,291 - INFO - Waiting for 1 pending streamed tool executions
2025-05-30 15:58:11 2025-05-30 10:28:11,292 - INFO - Executing tool: execute_slack_slack_updates_a_slack_message with arguments: {'action': 'SLACK_GET_MENTIONS', 'as_user': '', 'attachments': '', 'blocks': '', 'channel': '', 'link_names': '', 'parse': '', 'text': '', 'ts': ''}
2025-05-30 15:58:11 2025-05-30 10:28:11,292 - ERROR - Error executing tool execute_slack_slack_updates_a_slack_message: 'AgentPressXMLTool' object has no attribute 'execute_gmail_gmail_send_email'
2025-05-30 15:58:11 Traceback (most recent call last):
2025-05-30 15:58:11   File "/app/agentpress/response_processor.py", line 1566, in _execute_tool
2025-05-30 15:58:11     available_functions = self.tool_registry.get_available_functions()
2025-05-30 15:58:11                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-05-30 15:58:11   File "/app/agentpress/tool_registry.py", line 238, in get_available_functions
2025-05-30 15:58:11     function = getattr(tool_instance, method_name)
2025-05-30 15:58:11                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-05-30 15:58:11 AttributeError: 'AgentPressXMLTool' object has no attribute 'execute_gmail_gmail_send_email'
2025-05-30 15:58:11 Traceback (most recent call last):
2025-05-30 15:58:11   File "/app/agentpress/response_processor.py", line 1566, in _execute_tool
2025-05-30 15:58:11     available_functions = self.tool_registry.get_available_functions()
2025-05-30 15:58:11                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-05-30 15:58:11   File "/app/agentpress/tool_registry.py", line 238, in get_available_functions
2025-05-30 15:58:11     function = getattr(tool_instance, method_name)
2025-05-30 15:58:11                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-05-30 15:58:11 AttributeError: 'AgentPressXMLTool' object has no attribute 'execute_gmail_gmail_send_email'
2025-05-30 15:58:11 2025-05-30 10:28:11,301 - agentpress - DEBUG - Status for tool index 0 already yielded.
2025-05-30 15:58:11 2025-05-30 10:28:11,301 - agentpress - DEBUG - Adding message of type 'status' to thread 195c4895-d81c-451a-aac9-ac55f48a7d30
2025-05-30 15:58:11 2025-05-30 10:28:11,349 - httpcore.http2 - DEBUG - send_request_headers.started request=<Request [b'POST']> stream_id=39
2025-05-30 15:58:11 2025-05-30 10:28:11,350 - hpack.hpack - DEBUG - Adding (b':method', b'POST') to the header table, sensitive:False, huffman:True
2025-05-30 15:58:11 2025-05-30 10:28:11,351 - hpack.hpack - DEBUG - Encoding 3 with 7 bits
2025-05-30 15:58:11 2025-05-30 10:28:11,351 - hpack.hpack - DEBUG - Adding (b':authority', b'eygeicggvuphidsdrbac.supabase.co') to the header table, sensitive:False, huffman:True
2025-05-30 15:58:11 2025-05-30 10:28:11,351 - hpack.hpack - DEBUG - Encoding 92 with 7 bits
2025-05-30 15:58:11 2025-05-30 10:28:11,351 - hpack.hpack - DEBUG - Adding (b':scheme', b'https') to the header table, sensitive:False, huffman:True
2025-05-30 15:58:11 2025-05-30 10:28:11,351 - hpack.hpack - DEBUG - Encoding 7 with 7 bits
2025-05-30 15:58:11 2025-05-30 10:28:11,351 - hpack.hpack - DEBUG - Adding (b':path', b'/rest/v1
