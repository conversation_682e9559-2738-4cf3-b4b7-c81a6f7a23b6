"""
AgentPress Composio ToolSet

Custom Composio framework integration that leverages built-in patterns while
maintaining our XML tool calling approach. This replaces manual XML mapping
with automatic schema conversion from Composio's JSON schemas.

Based on Composio's framework integration patterns:
- https://docs.composio.dev/framework/
"""

import logging
import json
from typing import Dict, Any, List, Optional, Union, Sequence

# Try to import from both possible locations
try:
    from composio_openai import ComposioToolSet as BaseComposioToolSet, Action, App

    COMPOSIO_AVAILABLE = True
except ImportError:
    try:
        from composio import ComposioToolSet as BaseComposioToolSet, Action, App

        COMPOSIO_AVAILABLE = True
    except ImportError:
        COMPOSIO_AVAILABLE = False

        # Create mock classes for testing
        class BaseComposioToolSet:
            def __init__(self, *args, **kwargs):
                self.entity_id = kwargs.get("entity_id")
                self.api_key = kwargs.get("api_key")

        class Action:
            pass

        class App:
            NOTION = "NOTION"
            GMAIL = "GMAIL"


# Parameter mappings for common Composio actions
PARAMETER_MAPPINGS = {
    "GMAIL_SEND_EMAIL": {
        "to": "recipient_email",
        "subject": "subject",
        "body": "body",
        "cc": "cc",
        "bcc": "bcc",
    },
    "NOTION_CREATE_NOTION_PAGE": {
        "title": "title",
        "parent_id": "parent_id",
        "content": "content",
    },
    "SLACK_SEND_MESSAGE": {"channel": "channel", "text": "text", "message": "text"},
}

logger = logging.getLogger(__name__)


class AgentPressXMLTool:
    """
    XML tool wrapper for Composio actions.

    This class wraps a single Composio action as an XML tool that integrates
    seamlessly with AgentPress's XML tool calling system.
    """

    def __init__(
        self,
        action_name: str,
        service_name: str,
        description: str,
        schema_params: Dict[str, Any],
        toolset: "AgentPressComposioToolSet",
        entity_id: Optional[str] = None,
    ):
        """
        Initialize the XML tool wrapper.

        Args:
            action_name: Composio action name (e.g., "NOTION_CREATE_NOTION_PAGE")
            service_name: Service name (e.g., "notion")
            description: Action description
            schema_params: JSON schema parameters from Composio
            toolset: Reference to the parent toolset for execution
            entity_id: User entity ID for isolation
        """
        self.action_name = action_name
        self.service_name = service_name
        self.description = description
        self.schema_params = schema_params
        self.toolset = toolset
        self.entity_id = entity_id

        # Generate XML tag name
        self.xml_tag = f"{service_name}-action"

    @property
    def name(self) -> str:
        """Return the tool name."""
        return f"{self.service_name}_{self.action_name.lower()}"

    @property
    def xml_schema(self) -> str:
        """Generate XML schema from Composio's JSON schema."""
        return self._generate_xml_schema()

    def _generate_xml_schema(self) -> str:
        """
        Generate XML schema from Composio's JSON schema parameters.

        This method converts Composio's JSON schema to our XML format,
        leveraging existing parameter mappings where available.
        """
        # Get existing parameter mappings for this service/action
        service_mappings = PARAMETER_MAPPINGS.get(self.service_name, {})
        action_mappings = service_mappings.get(self.action_name, {})

        # Build schema description
        schema_parts = [
            f'<{self.xml_tag} action="{self.action_name}" [parameters]>',
            "  [optional_content]",
            f"</{self.xml_tag}>",
            "",
            f"{self.service_name.title()} Action: {self.action_name}",
            f"Description: {self.description}",
            "",
        ]

        # Add parameter information from JSON schema
        if "properties" in self.schema_params:
            schema_parts.append("Parameters:")
            required_params = self.schema_params.get("required", [])

            for param_name, param_info in self.schema_params["properties"].items():
                param_type = param_info.get("type", "string")
                param_desc = param_info.get("description", "")
                is_required = param_name in required_params

                # Check if we have a mapped parameter name
                mapped_name = param_name
                for user_param, composio_param in action_mappings.items():
                    if composio_param == param_name:
                        mapped_name = user_param
                        break

                required_marker = " (required)" if is_required else " (optional)"
                schema_parts.append(
                    f"- {mapped_name}: {param_type}{required_marker} - {param_desc}"
                )

        schema_parts.extend(
            [
                "",
                "Example:",
                f'<{self.xml_tag} action="{self.action_name}" param1="value1">',
                "  Optional content for body/message parameters",
                f"</{self.xml_tag}>",
            ]
        )

        return "\n".join(schema_parts)

    def get_xml_mappings(self) -> List[Dict[str, Any]]:
        """
        Get XML parameter mappings for this tool.

        Returns:
            List of XML node mappings for the tool
        """
        mappings = [
            {
                "param_name": "action",
                "node_type": "attribute",
                "path": ".",
                "required": True,
            }
        ]

        # Add mappings for each parameter in the schema
        if "properties" in self.schema_params:
            for param_name, param_info in self.schema_params["properties"].items():
                # Determine if parameter should be XML content vs attribute
                is_content = self._should_be_xml_content(param_info)

                mappings.append(
                    {
                        "param_name": param_name,
                        "node_type": "content" if is_content else "attribute",
                        "path": ".",
                        "required": param_name
                        in self.schema_params.get("required", []),
                    }
                )

        return mappings

    def get_xml_example(self) -> str:
        """
        Get an XML usage example for this tool.

        Returns:
            XML example string
        """
        # Get a sample action name for the example
        example_action = self.action_name

        # Build example parameters
        example_params = []
        content_example = ""

        if "properties" in self.schema_params:
            for param_name, param_info in self.schema_params["properties"].items():
                if self._should_be_xml_content(param_info):
                    content_example = f"  {param_info.get('description', 'Content for ' + param_name)}"
                else:
                    example_value = self._get_example_value(param_info)
                    example_params.append(f'{param_name}="{example_value}"')

        # Build the complete example
        params_str = " ".join(example_params)
        if params_str:
            params_str = " " + params_str

        if content_example:
            return f'<{self.xml_tag} action="{example_action}"{params_str}>\n{content_example}\n</{self.xml_tag}>'
        else:
            return f'<{self.xml_tag} action="{example_action}"{params_str} />'

    def _should_be_xml_content(self, param_info: Dict[str, Any]) -> bool:
        """
        Determine if parameter should be XML content vs attribute.

        Args:
            param_info: Parameter information from JSON schema

        Returns:
            True if parameter should be XML content, False for attribute
        """
        param_type = param_info.get("type", "string")
        description = param_info.get("description", "").lower()

        # Long text fields should be content
        if param_type == "string":
            # Check for content-like parameter names/descriptions
            content_indicators = [
                "body",
                "content",
                "message",
                "text",
                "description",
                "note",
            ]
            if any(indicator in description for indicator in content_indicators):
                return True

        # Objects and arrays should be content
        if param_type in ["object", "array"]:
            return True

        return False

    def _get_example_value(self, param_info: Dict[str, Any]) -> str:
        """
        Get an example value for a parameter.

        Args:
            param_info: Parameter information from JSON schema

        Returns:
            Example value as string
        """
        param_type = param_info.get("type", "string")

        if param_type == "string":
            return "example_value"
        elif param_type == "integer":
            return "123"
        elif param_type == "number":
            return "123.45"
        elif param_type == "boolean":
            return "true"
        else:
            return "example"

    async def execute(self, **kwargs) -> str:
        """
        Execute the Composio action with XML parameters.

        This method:
        1. Parses XML parameters and maps them to Composio format
        2. Executes the action via the parent toolset
        3. Formats and returns the result
        """
        logger.info(f"🎯 Executing {self.service_name} action {self.action_name}")
        logger.debug(f"📝 Received kwargs: {kwargs}")

        try:
            # Parse and map parameters
            mapped_params = self._parse_and_map_parameters(kwargs)
            logger.debug(f"📋 Mapped parameters: {mapped_params}")

            # Execute via toolset
            result = self.toolset.execute_action(
                action=self.action_name,
                params=mapped_params,
                entity_id=self.entity_id,
            )

            # Format result
            return self._format_result(result)

        except Exception as e:
            logger.error(f"❌ Error executing {self.action_name}: {e}")
            return f"❌ {self.service_name.title()} action failed: {str(e)}"

    def _parse_and_map_parameters(self, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse XML parameters and map them to Composio format.

        Uses existing parameter mappings from composio_processors.py
        """
        # Get parameter mappings for this action
        service_mappings = PARAMETER_MAPPINGS.get(self.service_name, {})
        action_mappings = service_mappings.get(self.action_name, {})

        mapped_params = {}

        for param_name, param_value in kwargs.items():
            if param_name == "action":
                continue  # Skip action parameter

            # Map parameter name if mapping exists
            composio_param_name = action_mappings.get(param_name, param_name)
            mapped_params[composio_param_name] = param_value

        return mapped_params

    def _format_result(self, result: Any) -> str:
        """Format the execution result for XML response."""
        try:
            if isinstance(result, dict):
                # Check for standard Composio response format
                if "successful" in result:
                    if result.get("successful"):
                        data = result.get("data", {})
                        return f"✅ {self.service_name.title()} action completed successfully.\n\nResult:\n{json.dumps(data, indent=2)}"
                    else:
                        error = result.get("error", "Unknown error")
                        return f"❌ {self.service_name.title()} action failed: {error}"
                else:
                    # Direct result data
                    return f"✅ {self.service_name.title()} action completed.\n\nResult:\n{json.dumps(result, indent=2)}"
            else:
                return f"✅ {self.service_name.title()} action completed.\n\nResult:\n{str(result)}"

        except Exception as e:
            logger.warning(f"Error formatting result: {e}")
            return f"✅ {self.service_name.title()} action completed successfully."


class AgentPressComposioToolSet(BaseComposioToolSet):
    """
    Custom Composio toolset for AgentPress XML integration.

    This class follows Composio's framework integration patterns while
    converting tools to our XML format automatically.
    """

    def __init__(self, *args, **kwargs):
        """Initialize the AgentPress Composio toolset."""
        if not COMPOSIO_AVAILABLE:
            logger.warning("⚠️ Composio SDK not available - running in mock mode")
            self.entity_id = kwargs.get("entity_id")
            self.api_key = kwargs.get("api_key")
            return

        super().__init__(
            *args,
            **kwargs,
            # AgentPress-specific configuration
            runtime="agentpress",
            description_char_limit=1024,
            action_name_char_limit=64,
        )
        logger.info(
            f"🏭 AgentPressComposioToolSet initialized with entity_id: {self.entity_id}"
        )

    def _wrap_tool(
        self,
        schema: Dict[str, Any],
        entity_id: Optional[str] = None,
    ) -> AgentPressXMLTool:
        """
        Enhanced tool wrapping with comprehensive parameter mapping.

        This is the core method that implements automatic schema conversion
        from Composio's JSON schemas to our XML tool format.
        """
        action_name = schema["name"]
        description = schema["description"]
        schema_params = schema.get("parameters", {})
        app_name = schema.get("appName", "unknown")

        # Extract service name with enhanced logic
        service_name = self._extract_service_name_enhanced(action_name, app_name)

        logger.debug(
            f"🔧 Wrapping {action_name} as XML tool for service {service_name}"
        )

        return AgentPressXMLTool(
            action_name=action_name,
            service_name=service_name,
            description=description,
            schema_params=schema_params,
            toolset=self,
            entity_id=entity_id or self.entity_id,
        )

    def _extract_service_name(self, action_name: str) -> str:
        """Extract service name from Composio action name."""
        # Composio action names follow pattern: SERVICE_ACTION_NAME
        parts = action_name.split("_")
        if len(parts) > 1:
            return parts[0].lower()
        return "unknown"

    def _extract_service_name_enhanced(self, action_name: str, app_name: str) -> str:
        """
        Enhanced service name extraction with app name fallback.

        Args:
            action_name: Composio action name (e.g., "GMAIL_SEND_EMAIL")
            app_name: App name from schema (e.g., "GMAIL")

        Returns:
            Service name in lowercase
        """
        # First try the standard extraction
        service_name = self._extract_service_name(action_name)

        # If we got "unknown", try using app_name
        if service_name == "unknown" and app_name != "unknown":
            service_name = app_name.lower()

        # Handle special service name mappings
        service_mappings = {
            "googledrive": "google_drive",
            "googlecalendar": "google_calendar",
            "googledocs": "google_docs",
            "googlesheets": "google_sheets",
        }

        return service_mappings.get(service_name, service_name)

    def _get_app_enum(self, service_name: str):
        """
        Map service name to Composio App enum.

        Args:
            service_name: Service name (e.g., "gmail", "notion")

        Returns:
            Composio App enum or None if not found
        """
        if not COMPOSIO_AVAILABLE:
            return None

        # Service name to App enum mapping
        app_mappings = {
            "gmail": App.GMAIL,
            "notion": App.NOTION,
            "slack": App.SLACK,
            "google_drive": App.GOOGLEDRIVE,
            "googledrive": App.GOOGLEDRIVE,
            "google_calendar": App.GOOGLECALENDAR,
            "googlecalendar": App.GOOGLECALENDAR,
        }

        return app_mappings.get(service_name.lower())

    def get_tools(
        self,
        actions: Optional[Sequence[Union[Action, str]]] = None,
        apps: Optional[Sequence[Union[App, str]]] = None,
        tags: Optional[List[str]] = None,
        entity_id: Optional[str] = None,
        **kwargs,
    ) -> List[AgentPressXMLTool]:
        """
        Get Composio tools as AgentPress XML tools.

        This method follows Composio's standard get_tools pattern while
        returning XML tools instead of native tools.
        """
        logger.info(f"🔍 Getting XML tools for entity {entity_id or self.entity_id}")

        if not COMPOSIO_AVAILABLE:
            logger.warning("⚠️ Composio SDK not available - returning empty tool list")
            return []

        try:
            # Get action schemas from Composio
            action_schemas = self.get_action_schemas(
                actions=actions, apps=apps, tags=tags, **kwargs
            )

            # Convert each schema to XML tool
            xml_tools = []
            for schema in action_schemas:
                try:
                    xml_tool = self._wrap_tool(
                        schema=schema.model_dump(exclude_none=True),
                        entity_id=entity_id or self.entity_id,
                    )
                    xml_tools.append(xml_tool)
                    logger.debug(f"✅ Created XML tool: {xml_tool.name}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to wrap tool {schema.name}: {e}")
                    continue

            logger.info(
                f"🎉 Created {len(xml_tools)} XML tools from {len(action_schemas)} schemas"
            )
            return xml_tools

        except Exception as e:
            logger.error(f"❌ Error getting XML tools: {e}")
            return []

    async def get_user_tools_dynamic(self, user_id: str) -> List[AgentPressXMLTool]:
        """
        Dynamically discover and create XML tools based on user's active connections.

        Args:
            user_id: User ID to get tools for

        Returns:
            List of XML tools for the user's active connections
        """
        logger.info(f"🔍 Dynamically discovering tools for user {user_id}")

        try:
            # Import here to avoid circular imports
            from services.composio_auth_service import ComposioAuthService

            # Get user's active connections
            auth_service = ComposioAuthService.from_env()
            active_connections = await auth_service.get_user_connections(user_id)

            # Filter for active connections
            active_services = [
                conn.service_name
                for conn in active_connections
                if conn.status == "active" and conn.is_active
            ]

            if not active_services:
                logger.info(f"ℹ️ No active connections found for user {user_id}")
                return []

            logger.info(
                f"📋 Found active services for user {user_id}: {active_services}"
            )

            # Map services to App enums
            apps_to_load = []
            for service_name in active_services:
                app_enum = self._get_app_enum(service_name)
                if app_enum:
                    apps_to_load.append(app_enum)
                else:
                    logger.warning(f"⚠️ No App enum found for service: {service_name}")

            if not apps_to_load:
                logger.warning(f"⚠️ No valid apps to load for user {user_id}")
                return []

            # Get XML tools for the user's apps
            xml_tools = self.get_tools(
                apps=apps_to_load, entity_id=user_id, check_connected_accounts=True
            )

            logger.info(
                f"🎉 Dynamically loaded {len(xml_tools)} tools for user {user_id}"
            )
            return xml_tools

        except Exception as e:
            logger.error(f"❌ Error in dynamic tool discovery for user {user_id}: {e}")
            import traceback

            traceback.print_exc()
            return []
